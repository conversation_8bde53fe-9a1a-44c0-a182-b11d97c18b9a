import { useLocale } from '@bika/contents/i18n';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/button-component';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import { directDownload } from '@bika/ui/preview-attachment/attachment-renderer';
import { FilePreview } from '@bika/ui/preview-attachment/file-preview';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preview-attachment';
import type { ToolUIPart } from 'ai';
import { ArtifactContainerWithModal } from '../ai/client/chat/artifacts/components/artifact-container-with-modal';

export interface GeneratedImage {
  type: 'image';
  data: string; // The image URL
  date: string;
  mimeType: string;
}
interface FileArtifactProps {
  dataList: GeneratedImage[];
  content?: string;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolUIPart;
  expandable?: boolean;
}

export const ImagesArtifact = (props: FileArtifactProps) => {
  const { content, dataList, skillsets = [], tool, expandable = true } = props;
  const { t } = useLocale();

  const data = { content, dataList };

  const triggerImage = (index: number) => {
    const attachmentsForPreview = (dataList ?? []).map((item) => {
      const generatedImage = item as GeneratedImage;
      return {
        name: `${index + 1}`,
        contentType: generatedImage.mimeType || 'image/png',
        url: generatedImage.data,
        variant: 'kkfile' as const,
      };
    });

    triggerPreviewAttachment({ index, attachments: attachmentsForPreview, t });
  };

  const downloadButton = (
    <>
      <IconButton
        variant="plain"
        size="sm"
        color="neutral"
        onClick={async () => {
          // Download all images
          if (dataList && dataList.length > 0) {
            for (const [index, image] of dataList.entries()) {
              const generatedImage = image as GeneratedImage;
              if (generatedImage.data) {
                const filename = `image_${index + 1}.${generatedImage.mimeType?.split('/')[1] || 'png'}`;
                await directDownload(generatedImage.data, filename);
              }
            }
          }
        }}
        sx={{
          '&:hover': {
            backgroundColor: 'var(--hover)',
          },
        }}
      >
        <DownloadOutlined color="var(--text-primary)" />
      </IconButton>
    </>
  );

  return (
    <ArtifactContainerWithModal
      data={data}
      skillsets={skillsets}
      tool={tool}
      modalProps={{
        width: '100vw',
        height: '100vh',
      }}
      expandable={expandable}
      rowDataType="json"
      toolbarButton={downloadButton}
      switchProps={{
        previewLabel: t.ai.artifact_preview,
      }}
    >
      {dataList.length === 1 && <FilePreview url={dataList?.[0]?.data} />}
      {dataList.length !== 1 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4">
          {dataList?.map((item, index) => {
            const generatedImage = item as GeneratedImage;

            return (
              <div
                key={index}
                className="relative aspect-square border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => {
                  triggerImage(index);
                }}
              >
                <img
                  alt={`Generated ${index + 1}`}
                  loading="lazy"
                  src={generatedImage.data}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Handle broken image
                    const target = e.target as HTMLImageElement;
                    target.src =
                      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDNWMjFIMTlWNUg1VjNIMjFaTTIwIDJINFYyMkgyMFYyWiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTEuNSA5QzEyLjMgOSAxMyA4LjMgMTMgNy41UzEyLjMgNiAxMS41IDZTMTAgNi43IDEwIDcuNVMxMC43IDkgMTEuNSA5WiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNNSAxNUwxMCAxMEwxMyAxM0wxNyA5TDE5IDE1SDVaIiBmaWxsPSIjOTk5OTk5Ii8+Cjwvc3ZnPgo=';
                  }}
                />
              </div>
            );
          })}
        </div>
      )}
    </ArtifactContainerWithModal>
  );
};
